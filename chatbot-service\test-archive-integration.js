/**
 * Test script for Archive Service Integration
 * Run this to test the chatbot service integration with archive service
 */

const archiveService = require('./src/services/archiveService');
const logger = require('./src/utils/logger');

async function testArchiveIntegration() {
  console.log('🧪 Testing Archive Service Integration...\n');

  // Test 1: Get user's latest assessment
  console.log('📋 Test 1: Get user latest assessment');
  try {
    const testUserId = '550e8400-e29b-41d4-a716-446655440000'; // Example user ID
    const latestAssessment = await archiveService.getUserLatestAssessment(testUserId);
    
    if (latestAssessment) {
      console.log('✅ Latest assessment found:', {
        id: latestAssessment.id,
        userId: latestAssessment.user_id,
        assessmentName: latestAssessment.assessment_name,
        status: latestAssessment.status,
        createdAt: latestAssessment.created_at
      });
    } else {
      console.log('ℹ️  No assessment found for user');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Get assessment by ID
  console.log('📋 Test 2: Get assessment by ID');
  try {
    const testResultId = 'result_550e8400-e29b-41d4-a716-446655440000'; // Example result ID
    const assessmentData = await archiveService.getAssessmentById(testResultId);
    
    if (assessmentData) {
      console.log('✅ Assessment data found:', {
        id: assessmentData.id,
        userId: assessmentData.user_id,
        assessmentName: assessmentData.assessment_name,
        status: assessmentData.status,
        hasPersonaProfile: !!assessmentData.persona_profile,
        archetype: assessmentData.persona_profile?.archetype || 'N/A'
      });
    } else {
      console.log('ℹ️  Assessment not found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Validate user assessment access
  console.log('📋 Test 3: Validate user assessment access');
  try {
    const testUserId = '550e8400-e29b-41d4-a716-446655440000';
    const testResultId = 'result_550e8400-e29b-41d4-a716-446655440000';
    const validationResult = await archiveService.validateUserAssessmentAccess(testUserId, testResultId);
    
    if (validationResult) {
      console.log('✅ User has access to assessment:', {
        id: validationResult.id,
        userId: validationResult.user_id,
        assessmentName: validationResult.assessment_name
      });
    } else {
      console.log('❌ User does not have access to assessment or assessment not found');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Get user assessments with pagination
  console.log('📋 Test 4: Get user assessments with pagination');
  try {
    const testUserId = '550e8400-e29b-41d4-a716-446655440000';
    const userAssessments = await archiveService.getUserAssessments(testUserId, {
      page: 1,
      limit: 5,
      status: 'completed'
    });
    
    if (userAssessments && userAssessments.results) {
      console.log('✅ User assessments found:', {
        totalResults: userAssessments.pagination?.total || 0,
        currentPage: userAssessments.pagination?.page || 1,
        resultsCount: userAssessments.results.length,
        results: userAssessments.results.map(r => ({
          id: r.id,
          assessmentName: r.assessment_name,
          status: r.status,
          createdAt: r.created_at
        }))
      });
    } else {
      console.log('ℹ️  No assessments found for user');
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }

  console.log('\n🏁 Archive Service Integration Test Complete!\n');
}

// Run the test if this file is executed directly
if (require.main === module) {
  testArchiveIntegration().catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testArchiveIntegration };
